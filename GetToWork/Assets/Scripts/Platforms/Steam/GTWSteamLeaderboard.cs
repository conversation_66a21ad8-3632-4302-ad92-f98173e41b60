// Copyright Isto Inc.

using Isto.GTW.Data;
using Isto.GTW.Leaderboards;
using Isto.GTW.UI;
using Steamworks;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.Managers
{
    public class GTWSteamLeaderboard : MonoBehaviour, ILeaderboard
    {
        // OTHER FIELDS

        // Steamworks leaderboard name
        private static string LEADERBOARD_NAME = "Doinkler Special Highest Checkpoint";

        // Leaderboard display range
        private static int START_RANK = 1;
        private static int END_RANK = 50;

        // Persistence in case of a crash
        private static readonly string PENDING_UPLOAD_KEY = "GTW_PendingLeaderboardScore";

        // Steam call results
        private CallResult<LeaderboardScoresDownloaded_t> _downloadScoresResult;
        private CallResult<LeaderboardFindResult_t> _findLeaderboardResult;
        private CallResult<LeaderboardScoreUploaded_t> _uploadScoreResult;

        private SteamLeaderboard_t _steamLeaderboard;
        private List<LeaderboardEntryData> _leaderboardEntries = new List<LeaderboardEntryData>();

        private bool _leaderboardFound;
        private int _highestCheckpoint;

        // Rate limiting fields
        // Steam allows 10 uploads per 10 minutes.
        // We keep an upload on hand to ensure that if the user quits while having a checkpoint cached, we are still able to upload it.
        // This leaves 9 uploads for the user to use while playing.
        // We then give a buffer of 1 upload to account for any network delays or other issues.
        // This leaves 8 uploads for the user to use while playing per 10 minutes.
        private static readonly int MAX_UPLOADS_PER_WINDOW = 8;
        private static readonly float RATE_LIMIT_WINDOW_SECONDS = 600f; // 10 minutes

        private List<float> _uploadTimestamps = new List<float>();
        private int? _pendingUploadScore = null;
        private bool _hasOutstandingUpload = false;
        private bool _rateLimitMonitorActive = false;
        private bool _isInitialSetupComplete = false;


        // PROPERTIES

        public string LeaderboardName => SteamFriends.GetPersonaName();
        public List<LeaderboardEntryData> LeaderboardEntries => _leaderboardEntries;


        // EVENTS

        public event Action OnCheckpointsDownloaded;


        // LIFECYCLE EVENTS

        private void Start()
        {
            if (!SteamManager.Initialized)
            {
                Debug.LogError("[LeaderboardManager] SteamManager not initialized. Cannot find or create leaderboard.");
                return;
            }

            RegisterEvents();

            SetupLeaderboard();
        }

        private void OnDestroy()
        {
            UnregisterEvents();

            if (_rateLimitMonitorActive)
            {
                _rateLimitMonitorActive = false;
            }

            StopAllCoroutines();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Application.quitting += Application_OnQuit;
            _findLeaderboardResult = CallResult<LeaderboardFindResult_t>.Create(SteamLeaderboard_OnFindLeaderboard);
            _uploadScoreResult = CallResult<LeaderboardScoreUploaded_t>.Create(SteamLeaderboard_OnUploadScore);
            _downloadScoresResult = CallResult<LeaderboardScoresDownloaded_t>.Create(SteamLeaderboard_OnDownloadScores);
        }

        private void UnregisterEvents()
        {
            Application.quitting -= Application_OnQuit;
            _findLeaderboardResult?.Cancel();
            _uploadScoreResult?.Cancel();
            _downloadScoresResult?.Cancel();
        }

        private void Application_OnQuit()
        {
            // Try to upload any pending score before quitting
            if (_pendingUploadScore.HasValue && _leaderboardFound && !_hasOutstandingUpload)
            {
                Debug.Log($"[LeaderboardManager] Application quitting - attempting to upload pending score {_pendingUploadScore.Value}");

                // Force upload the pending score, bypassing rate limiting for application quit
                ForceUploadOnQuit(_pendingUploadScore.Value);
            }
        }

        private void SteamLeaderboard_OnFindLeaderboard(LeaderboardFindResult_t result, bool isIOFailure)
        {
            if (isIOFailure || result.m_bLeaderboardFound == 0)
            {
                Debug.LogError($"[LeaderboardManager] Failed to find or create leaderboard '{LEADERBOARD_NAME}'. IOFailure? {isIOFailure}");
                return;
            }

            _steamLeaderboard = result.m_hSteamLeaderboard;
            _leaderboardFound = true;
            Debug.Log($"[LeaderboardManager] Found or created leaderboard '{LEADERBOARD_NAME}' (Handle: {_steamLeaderboard.m_SteamLeaderboard}).");
            DownloadFriendsEntries();
        }

        private void SteamLeaderboard_OnUploadScore(LeaderboardScoreUploaded_t result, bool isIOFailure)
        {
            _hasOutstandingUpload = false;

            if (isIOFailure || result.m_bSuccess == 0)
            {
                Debug.LogError($"[LeaderboardManager] Score upload failed. IOFailure? {isIOFailure}. Will Queue for next available upload.");
                QueueUpload(result.m_nScore);
            }
            else
            {
                Debug.Log($"[LeaderboardManager] Upload succeeded. New rank: {result.m_nGlobalRankNew}, Score: {result.m_nScore}");
            }
        }

        private void SteamLeaderboard_OnDownloadScores(LeaderboardScoresDownloaded_t result, bool isIOFailure)
        {
            if (isIOFailure)
            {
                Debug.LogError($"[LeaderboardManager] Failed to download leaderboard entries. IOFailure? {isIOFailure}");
                return;
            }

            Debug.Log("[LeaderboardManager] Downloading leaderboard entries...");

            int count = result.m_cEntryCount;
            List<LeaderboardEntryData> entries = new List<LeaderboardEntryData>(count);

            for (int i = 0; i < count; i++)
            {
                LeaderboardEntry_t entry;
                SteamUserStats.GetDownloadedLeaderboardEntry(
                    result.m_hSteamLeaderboardEntries,
                    i,
                    out entry,
                    null,
                    0
                );

                string playerName = SteamFriends.GetFriendPersonaName(entry.m_steamIDUser);
                int globalRank = entry.m_nGlobalRank;
                int localRank = i + 1;

                Texture2D avatarTex = GetPlayerAvatar(entry);

                LeaderboardEntryData data = new LeaderboardEntryData(
                    playerName,
                    entry.m_nScore,
                    globalRank,
                    localRank,
                    avatarTex
                );
                entries.Add(data);
            }

            FindAndSetPlayersHighestScoreFromLeaderboard(entries);

            _leaderboardEntries = entries;

            Debug.Log("[LeaderboardManager] Downloading leaderboard entries complete!");

            // Only want to attempt to restore pending uploads once we have downloaded the latest scores
            // and only once during setup
            if(!_isInitialSetupComplete)
            {
                RestorePendingUploads();
            }
            OnCheckpointsDownloaded?.Invoke();
        }


        // ACCESSORS

        private static Texture2D GetPlayerAvatar(LeaderboardEntry_t entry)
        {
            SteamFriends.RequestUserInformation(entry.m_steamIDUser, true);

            int avatarHandle = SteamFriends.GetLargeFriendAvatar(entry.m_steamIDUser);
            Texture2D avatarTextyre = null;

            if (avatarHandle != -1)
            {
                if (SteamUtils.GetImageSize(avatarHandle, out uint width, out uint height))
                {
                    byte[] raw = new byte[width * height * 4]; // We multiply by 4 as RGBA is 4 bytes per pixel
                    if (SteamUtils.GetImageRGBA(avatarHandle, raw, raw.Length))
                    {
                        avatarTextyre = new Texture2D((int)width, (int)height, TextureFormat.RGBA32, false);
                        avatarTextyre.LoadRawTextureData(raw);
                        avatarTextyre.Apply();

                        // Since the coordinates are different between Unity and Steam, we need to flip the texture vertically
                        avatarTextyre = GTWUtils.FlipTextureVertically(avatarTextyre);

                    }
                }
            }

            return avatarTextyre;
        }


        // OTHER METHODS

        public void UploadHighestCheckpoint(int score)
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] UploadScore called too early. Leaderboard not yet found.");
                return;
            }

            if (score > _highestCheckpoint)
            {
                _highestCheckpoint = score;

                if (CanUploadImmediately())
                {
                    PerformUpload(score);
                }
                else
                {
                    QueueUpload(score);
                }
            }
        }

        public void DownloadFriendsEntries()
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] DownloadTopEntries called too early. Leaderboard not yet found.");
                return;
            }

            SteamAPICall_t call = SteamUserStats.DownloadLeaderboardEntries(
                _steamLeaderboard,
                ELeaderboardDataRequest.k_ELeaderboardDataRequestFriends,
                START_RANK,
                END_RANK
            );

            _downloadScoresResult.Set(call);
            Debug.Log($"[LeaderboardManager] Requesting entries {START_RANK}–{END_RANK}...");
        }

        private void FindAndSetPlayersHighestScoreFromLeaderboard(List<LeaderboardEntryData> entries)
        {
            LeaderboardEntryData playerEntryData = entries.Find(x => x.PlayerName == LeaderboardName);
            if(playerEntryData != null)
            {
                _highestCheckpoint = playerEntryData.HighestCheckpoint;
            }
            else
            {
                _highestCheckpoint = 0;
            }
        }

        private void SetupLeaderboard()
        {
            _isInitialSetupComplete = false;
            SteamAPICall_t handle = SteamUserStats.FindOrCreateLeaderboard(
                LEADERBOARD_NAME,
                ELeaderboardSortMethod.k_ELeaderboardSortMethodDescending,
                ELeaderboardDisplayType.k_ELeaderboardDisplayTypeNumeric
            );
            _findLeaderboardResult.Set(handle);

            Debug.Log($"[LeaderboardManager] Attempting to find or create leaderboard '{LEADERBOARD_NAME}'...");
        }


        // OTHER: Leaderboard Score Upload Rate Limiting

        private bool CanUploadImmediately()
        {
            if (_hasOutstandingUpload)
            {
                return false;
            }

            CleanupOldTimestamps();
            return _uploadTimestamps.Count < MAX_UPLOADS_PER_WINDOW;
        }

        private void CleanupOldTimestamps()
        {
            float currentTime = Time.time;
            float cutoffTime = currentTime - RATE_LIMIT_WINDOW_SECONDS;

            for (int i = _uploadTimestamps.Count - 1; i >= 0; i--)
            {
                if (_uploadTimestamps[i] < cutoffTime)
                {
                    _uploadTimestamps.RemoveAt(i);
                }
            }
        }

        private void PerformUpload(int score)
        {
            _hasOutstandingUpload = true;
            _uploadTimestamps.Add(Time.time);

            SteamAPICall_t call = SteamUserStats.UploadLeaderboardScore(
                _steamLeaderboard,
                ELeaderboardUploadScoreMethod.k_ELeaderboardUploadScoreMethodForceUpdate,
                score,
                null,
                0
            );
            _uploadScoreResult.Set(call);
            Debug.Log($"[LeaderboardManager] Uploading score {score}...");
        }

        private void QueueUpload(int score)
        {
            // Only keep the highest score to avoid unnecessary uploads
            if (!_pendingUploadScore.HasValue || score > _pendingUploadScore.Value)
            {
                _pendingUploadScore = score;
                Debug.Log($"[LeaderboardManager] Queued score {score} for upload (rate limited).");

                // Save to disk for crash recovery
                SavePendingUploadToDisk(score);

                // Start monitoring rate limits
                StartRateLimitMonitoring();
            }
        }

        private void ProcessPendingUploads()
        {
            if (_pendingUploadScore.HasValue && CanUploadImmediately())
            {
                int score = _pendingUploadScore.Value;
                _pendingUploadScore = null;

                // Clear from disk since we're uploading it
                ClearPendingUploadFromDisk();

                PerformUpload(score);

                // Stop monitoring if no more pending uploads
                if (!_pendingUploadScore.HasValue)
                {
                    StopRateLimitMonitoring();
                }
            }
        }

        private void ForceUploadOnQuit(int score)
        {
            // Clear the pending score since we're uploading it
            _pendingUploadScore = null;

            // Clear from disk since we're uploading it
            ClearPendingUploadFromDisk();

            // Mark as having outstanding upload to prevent other uploads
            _hasOutstandingUpload = true;

            SteamAPICall_t call = SteamUserStats.UploadLeaderboardScore(
                _steamLeaderboard,
                ELeaderboardUploadScoreMethod.k_ELeaderboardUploadScoreMethodForceUpdate,
                score,
                null,
                0
            );
            _uploadScoreResult.Set(call);
            Debug.Log($"[LeaderboardManager] Force uploading score {score} on application quit (bypassing rate limits)...");
        }

        private void StartRateLimitMonitoring()
        {
            // Only start rate limit monitoring if not already active
            if (!_rateLimitMonitorActive)
            {
                _rateLimitMonitorActive = true;
                StartCoroutine(RateLimitMonitorCoroutine());
                Debug.Log("[LeaderboardManager] Started rate limit monitoring for pending uploads.");
            }
        }

        private void StopRateLimitMonitoring()
        {
            if (_rateLimitMonitorActive)
            {
                _rateLimitMonitorActive = false;
                Debug.Log("[LeaderboardManager] Stopped rate limit monitoring.");
            }
        }

        private IEnumerator RateLimitMonitorCoroutine()
        {
            while (_rateLimitMonitorActive && _pendingUploadScore.HasValue)
            {
                // Check every 5 seconds if we can process pending uploads
                yield return new WaitForSeconds(5f);

                if (_pendingUploadScore.HasValue)
                {
                    ProcessPendingUploads();
                }
            }

            _rateLimitMonitorActive = false;
        }


        // OTHER: Persistence in case of an application crash

        private void SavePendingUploadToDisk(int score)
        {
            PlayerPrefs.SetInt(PENDING_UPLOAD_KEY, score);
        }

        private void ClearPendingUploadFromDisk()
        {
            if (!PlayerPrefs.HasKey(PENDING_UPLOAD_KEY))
                return;

            PlayerPrefs.DeleteKey(PENDING_UPLOAD_KEY);
            Debug.Log("[LeaderboardManager] Cleared pending upload from disk.");
        }

        private void RestorePendingUploads()
        {
            if (PlayerPrefs.HasKey(PENDING_UPLOAD_KEY))
            {
                Debug.Log("[LeaderboardManager] Checking pending upload from disk...");
                int savedScore = PlayerPrefs.GetInt(PENDING_UPLOAD_KEY);

                if(savedScore > _highestCheckpoint)
                {
                    Debug.Log("[LeaderboardManager] Pending upload found. Uploading to leaderboards.");
                    UploadHighestCheckpoint(savedScore);
                }
                else
                {
                    Debug.Log("[LeaderboardManager] Pending upload score is not greater than current highest checkpoint. Cancelling upload.");
                }

                ClearPendingUploadFromDisk();
            }

            _isInitialSetupComplete = true;
        }
    }
}